'use client';

import { useToast } from '@/components/ui/use-toast';
import { useCollections, useTranslation } from '@/contexts';
import { useDebounce } from '@/lib';
import { WordDetail } from '@/models';
import { Definition, Example, Explain, PartsOfSpeech, Word } from '@prisma/client';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { EmptyCollection, ListSkeleton, WordCard } from '@/components/ui';
import { Input } from '@/components/ui/input';
import { Translate } from '@/components/ui/translate';
import { motion } from 'framer-motion';
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogDescription,
	DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

export function MyWordsClient() {
	const { t } = useTranslation();
	const { toast } = useToast();
	const {
		currentCollection,
		loading,
		currentCollectionWords,
		searchWords,
		fetchCurrentCollectionWords,
		removeWordsFromCurrentCollection,
		refreshCurrentCollection,
	} = useCollections();

	// State
	const [searchQuery, setSearchQuery] = useState('');
	const collection = currentCollection;
	const [wordActionLoading, setWordActionLoading] = useState<
		Record<string, { removing?: boolean }>
	>({});
	const [wordIdToDelete, setWordIdToDelete] = useState<string | null>(null);
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

	const debouncedSearchQuery = useDebounce(searchQuery, 300);
	const isSearching = debouncedSearchQuery.trim().length > 0;

	useEffect(() => {
		if (currentCollection && debouncedSearchQuery.trim())
			searchWords(debouncedSearchQuery.trim());
		else if (currentCollection && !debouncedSearchQuery.trim()) fetchCurrentCollectionWords();
	}, [debouncedSearchQuery, currentCollection]);

	// Transform words data
	const wordsToDisplay = useMemo(() => {
		if (!currentCollection || !collection) return [];

		return (currentCollectionWords || []).map(
			(
				word: Word & {
					definitions?: (Definition & {
						explains?: Explain[];
						examples?: Example[];
						images?: string[];
					})[];
				}
			) =>
				({
					id: word.id,
					term: word.term,
					language: word.language,
					audio_url: word.audio_url ?? null,
					source_language: collection.source_language,
					target_language: collection.target_language,
					collection_id: collection.id,
					created_at: word.created_at,
					updated_at: word.updated_at,
					definitions: (word.definitions || []).map((def) => ({
						id: def.id,
						word_id: word.id,
						pos: def.pos as PartsOfSpeech[],
						ipa: def.ipa as string,
						images: def.images || [],
						explains: (def.explains || []).map((ex) => ({
							id: ex.id,
							EN: ex.EN,
							VI: ex.VI,
							definition_id: ex.definition_id,
						})),
						examples: (def.examples || []).map((ex) => ({
							id: ex.id,
							EN: ex.EN,
							VI: ex.VI,
							definition_id: ex.definition_id,
						})),
					})),
				} as WordDetail)
		);
	}, [currentCollectionWords, collection, currentCollection]);

	// Handlers
	const handleDeleteWord = useCallback((wordId: string) => {
		setWordIdToDelete(wordId);
		setIsDeleteDialogOpen(true);
	}, []);

	const confirmDeleteWord = useCallback(async () => {
		if (!wordIdToDelete || !currentCollection) return;

		setWordActionLoading((prev) => ({ ...prev, [wordIdToDelete]: { removing: true } }));

		try {
			await removeWordsFromCurrentCollection([wordIdToDelete]);
			toast({ title: t('collections.remove_word_success') });
			setIsDeleteDialogOpen(false);
			setWordIdToDelete(null);
			await Promise.all([fetchCurrentCollectionWords(), refreshCurrentCollection()]);
		} catch (error) {
			const err = error instanceof Error ? error : new Error(String(error));
			toast({
				variant: 'destructive',
				title: t('collections.remove_word_error'),
				description: err.message,
			});
		} finally {
			setWordActionLoading((prev) => ({ ...prev, [wordIdToDelete]: { removing: false } }));
		}
	}, [wordIdToDelete, currentCollection]);

	// Loading state
	if (loading.fetchWords && wordsToDisplay.length === 0 && !searchQuery) {
		return <div>Loading words...</div>;
	}

	// Render content based on state
	const renderContent = () => {
		if (loading.wordsSearch) return <ListSkeleton />;

		if (isSearching && wordsToDisplay.length === 0) {
			return (
				<div className="flex justify-center py-8 text-muted-foreground">
					<Translate text="ui.no_results_found" />
				</div>
			);
		}

		if (!isSearching && loading.fetchWords) {
			return <ListSkeleton />;
		}

		if (!isSearching && wordsToDisplay.length === 0) {
			return <EmptyCollection />;
		}

		if (wordsToDisplay.length > 0) {
			return (
				<motion.div
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					className="flex flex-col gap-4"
				>
					{wordsToDisplay.map((word) => (
						<WordCard
							key={word.id}
							word={word}
							onDeleteWord={() => handleDeleteWord(word.id)}
							isDeleting={!!wordActionLoading[word.id]?.removing}
						/>
					))}
				</motion.div>
			);
		}

		return null;
	};

	return (
		<>
			<section className="mt-8">
				<div className="mb-8">
					<h2 className="text-xl font-semibold mb-4">
						<Translate text="collections.words" />
					</h2>
					<Input
						type="text"
						placeholder="Search words…"
						value={searchQuery}
						onChange={(e) => setSearchQuery(e.target.value)}
					/>
				</div>
				<div className="mt-8">{renderContent()}</div>
			</section>

			<Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>
							<Translate text="collections.remove_word" />
						</DialogTitle>
						<DialogDescription>
							<Translate text="collections.remove_word_confirm" />
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
							<Translate text="ui.cancel" />
						</Button>
						<Button
							variant="destructive"
							onClick={confirmDeleteWord}
							disabled={
								!!(wordIdToDelete && wordActionLoading[wordIdToDelete]?.removing)
							}
						>
							<Translate text="ui.remove" />
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</>
	);
}
