'use client';

import { ListSkeleton, useToast } from '@/components/ui';
import { useCollections, useKeywords, useLLM, useLoading, useTranslation } from '@/contexts';
import { RandomWord, WordDetail } from '@/models';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { WordGenerationForm } from '../../components/word-generation-form';
import { WordList } from '../../components/word-list';
import { useWordActionState } from '../../components/hooks/use-word-action-state';

const MAX_TERMS_TO_GENERATE = 10;

export function GenerateWordsClient({ params }: { params: { id: string } }) {
	const { t } = useTranslation();
	const { toast } = useToast();
	const { currentCollection, loading, error } = useCollections();
	const {
		keywords,
		createKeyword,
		deleteKeyword,
		fetchKeywords,
		isLoading: keywordsLoadingState,
	} = useKeywords();
	const { generateRandomTerms, generateWordDetails, isLoading: isLlmHookLoading } = useLLM();
	const { addTermToCurrentCollection, addWordsToCurrentCollection, refreshCurrentCollection } =
		useCollections();
	const { setLoading: setGlobalLoading } = useLoading();

	const collection = currentCollection;
	const isLoading = loading.get || loading.setCurrent;

	// State
	const [selectedKeywords, setSelectedKeywords] = useState<string[]>([]);
	const [randomWords, setRandomWords] = useState<RandomWord[]>([]);
	const [detailedWords, setDetailedWords] = useState<Record<string, WordDetail>>({});
	const [generatingWords, setGeneratingWords] = useState(false);
	const { wordActionLoading, setWordActionState, clearWordActionState, getWordActionState } =
		useWordActionState();

	useEffect(() => {
		fetchKeywords();
	}, []);

	// Computed values
	const keywordIdToNameMap = useMemo(() => {
		const map: Record<string, string> = {};
		(keywords || []).forEach((k) => (map[k.id] = k.content));
		return map;
	}, [keywords]);

	const getKeywordNameFromId = useCallback(
		(keywordId: string): string => keywordIdToNameMap[keywordId] || keywordId,
		[keywordIdToNameMap]
	);

	const keywordsData = collection ? keywords || [] : [];

	// Global loading effect
	useEffect(() => {
		if (!collection) return;
		const anyLoading = generatingWords || keywordsLoadingState || isLlmHookLoading;
		setGlobalLoading(anyLoading);
	}, [generatingWords, keywordsLoadingState, isLlmHookLoading, setGlobalLoading, collection]);

	// Handlers
	const handleCreateKeyword = useCallback(
		async (name: string) => {
			try {
				const newKeyword = await createKeyword(name);
				if (!newKeyword) throw new Error(t('keywords.create_failed'));
				toast({
					title: t('keywords.created_title'),
					description: t('keywords.created_desc', { name }),
				});
				return newKeyword;
			} catch (error: unknown) {
				toast({
					variant: 'destructive',
					title: t('keywords.create_failed_title'),
					description: error instanceof Error ? error.message : String(error),
				});
				throw error;
			}
		},
		[createKeyword, toast, t]
	);

	const handleDeleteKeyword = useCallback(
		async (keywordId: string) => {
			try {
				await deleteKeyword(keywordId);
				setSelectedKeywords((prev) => prev.filter((id) => id !== keywordId));
				toast({ title: t('keywords.deleted_title') });
			} catch (error: unknown) {
				toast({
					variant: 'destructive',
					title: t('keywords.delete_failed_title'),
					description: error instanceof Error ? error.message : String(error),
				});
			}
		},
		[deleteKeyword, toast, t]
	);

	const handleGenerateWords = useCallback(async () => {
		if (!collection) {
			toast({ variant: 'destructive', title: t('collections.not_loaded_error') });
			return;
		}
		if (selectedKeywords.length === 0) {
			toast({
				variant: 'destructive',
				title: t('keywords.no_keywords_selected'),
				description: t('keywords.select_at_least_one'),
			});
			return;
		}
		setGeneratingWords(true);
		try {
			const keywordNames = selectedKeywords.map(getKeywordNameFromId);
			const generatedTerms = await generateRandomTerms({
				keywords: keywordNames,
				max_terms: MAX_TERMS_TO_GENERATE,
				exclude_collection_ids: [collection.id],
				source_language: collection.source_language,
				target_language: collection.target_language,
			});
			setRandomWords(generatedTerms);
		} catch (error: unknown) {
			const err = error instanceof Error ? error : new Error(String(error));
			toast({
				variant: 'destructive',
				title: t('words.generation_failed'),
				description: err.message,
			});
		} finally {
			setGeneratingWords(false);
		}
	}, [selectedKeywords, getKeywordNameFromId, generateRandomTerms, collection, toast, t]);

	const handleGetDetails = useCallback(
		async (word: RandomWord) => {
			if (!collection) {
				toast({ variant: 'destructive', title: t('collections.not_loaded_error') });
				return;
			}
			if (getWordActionState(word.term).gettingDetail || detailedWords[word.term]) return;

			setWordActionState(word.term, { gettingDetail: true, error: null });

			try {
				const detailsList = await generateWordDetails(
					[word.term],
					collection.source_language,
					collection.target_language
				);
				if (detailsList && detailsList.length > 0) {
					setDetailedWords((prev) => ({
						...prev,
						[word.term]: detailsList[0] as WordDetail,
					}));
				} else {
					throw new Error(t('words.detail_fetch_no_data', { term: word.term }));
				}
			} catch (error: unknown) {
				const err = error instanceof Error ? error : new Error(String(error));
				setWordActionState(word.term, { gettingDetail: false, error: err });
				toast({
					variant: 'destructive',
					title: t('words.detail_fetch_error'),
					description: err.message,
				});
			} finally {
				setWordActionState(word.term, { gettingDetail: false });
			}
		},
		[
			generateWordDetails,
			collection,
			toast,
			t,
			getWordActionState,
			detailedWords,
			setWordActionState,
		]
	);

	const handleAddToCollection = useCallback(
		async (word: RandomWord) => {
			if (!collection) {
				toast({ variant: 'destructive', title: t('collections.not_loaded_error') });
				return;
			}
			if (getWordActionState(word.term).adding) return;

			setWordActionState(word.term, { adding: true, error: null });

			try {
				if (detailedWords[word.term]?.id) {
					await addWordsToCurrentCollection([detailedWords[word.term].id]);
				} else {
					await addTermToCurrentCollection(word.term, collection.target_language);
				}
				toast({
					title: t('words.word_added'),
					description: t('words.word_added_desc', { term: word.term }),
				});
				await refreshCurrentCollection();

				setRandomWords((prev) => prev.filter((rw) => rw.term !== word.term));
				setDetailedWords((prev) => {
					const newDetails = { ...prev };
					delete newDetails[word.term];
					return newDetails;
				});
				clearWordActionState(word.term);
			} catch (error: unknown) {
				const err = error instanceof Error ? error : new Error(String(error));
				setWordActionState(word.term, { adding: false, error: err });
				toast({
					variant: 'destructive',
					title: t('words.add_error'),
					description: t('words.add_error_desc', {
						term: word.term,
						message: err.message,
					}),
				});
			} finally {
				setWordActionState(word.term, { adding: false });
			}
		},
		[
			addWordsToCurrentCollection,
			addTermToCurrentCollection,
			collection,
			detailedWords,
			getWordActionState,
			refreshCurrentCollection,
			setWordActionState,
			clearWordActionState,
			toast,
			t,
		]
	);

	if (isLoading) {
		return <ListSkeleton />;
	}

	return (
		<div className="space-y-6">
			<WordGenerationForm
				keywords={keywordsData}
				selectedKeywords={selectedKeywords}
				onKeywordsChangeAction={setSelectedKeywords}
				onCreateKeywordAction={handleCreateKeyword}
				onDeleteKeywordAction={handleDeleteKeyword}
				onGenerateAction={handleGenerateWords}
				generatingLoading={generatingWords}
			/>

			<WordList
				words={randomWords}
				detailedWords={detailedWords}
				onGetDetails={handleGetDetails}
				onAddToCollection={handleAddToCollection}
				getLoadingState={getWordActionState}
			/>
		</div>
	);
}
