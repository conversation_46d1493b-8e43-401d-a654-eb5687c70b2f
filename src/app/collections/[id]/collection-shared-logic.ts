'use client';

import { useToast } from '@/components/ui';
import { useCollections, useKeywords, useTranslation } from '@/contexts';
import { Difficulty, Keyword } from '@prisma/client';
import { useCallback, useEffect, useMemo, useState } from 'react';

export interface SharedPracticeLogicResult {
	// Keyword related
	keywords: Keyword[];
	selectedKeywords: string[];
	setSelectedKeywords: (ids: string[]) => void;
	handleCreateKeyword: (name: string) => Promise<Keyword | undefined>;
	handleDeleteKeyword: (id: string) => Promise<void>;
	getKeywordNameFromId: (keywordId: string) => string;

	// Difficulty related
	difficulty: Difficulty;
	setDifficulty: (d: Difficulty) => void;

	// Common utilities
	t: (key: string, params?: any) => string;
	currentCollection: any;
	toast: any;
}

export function useSharedPracticeLogic(): SharedPracticeLogicResult {
	const { t } = useTranslation();
	const { currentCollection } = useCollections();
	const { toast } = useToast();

	const {
		keywords,
		createKeyword,
		deleteKeyword: originalDeleteKeyword,
		fetchKeywords,
		isLoading: keywordsLoading,
	} = useKeywords();
	const [selectedKeywords, setSelectedKeywords] = useState<string[]>([]);
	const [difficulty, setDifficulty] = useState<Difficulty>(Difficulty.INTERMEDIATE);

	useEffect(() => {
		if (currentCollection?.id) {
			fetchKeywords();
		}
	}, [currentCollection]);

	const keywordIdToNameMap = useMemo(() => {
		const map: Record<string, string> = {};
		(keywords || []).forEach((k) => (map[k.id] = k.content));
		return map;
	}, [keywords]);

	const getKeywordNameFromId = useCallback(
		(keywordId: string): string => keywordIdToNameMap[keywordId] || keywordId,
		[keywordIdToNameMap]
	);

	const handleCreateKeyword = useCallback(async (name: string) => {
		try {
			const newKeyword = await createKeyword(name);
			if (!newKeyword) {
				toast({
					variant: 'destructive',
					title: t('keywords.create_failed_title'),
					description: t('keywords.create_failed'),
				});
				return undefined;
			}
			toast({
				title: t('keywords.created_title'),
				description: t('keywords.created_desc', { name }),
			});
			return newKeyword;
		} catch (error) {
			toast({
				variant: 'destructive',
				title: t('keywords.create_failed_title'),
				description: error instanceof Error ? error.message : String(error),
			});
			return undefined;
		}
	}, []);

	const handleDeleteKeyword = useCallback(async (keywordId: string) => {
		try {
			await originalDeleteKeyword(keywordId);
			setSelectedKeywords((prev) => prev.filter((id) => id !== keywordId));
			toast({ title: t('keywords.deleted_title') });
		} catch (error) {
			toast({
				variant: 'destructive',
				title: t('keywords.delete_failed_title'),
				description: error instanceof Error ? error.message : String(error),
			});
		}
	}, []);

	return {
		keywords,
		selectedKeywords,
		setSelectedKeywords,
		handleCreateKeyword,
		handleDeleteKeyword,
		getKeywordNameFromId,
		difficulty,
		setDifficulty,
		t,
		currentCollection,
		toast,
	};
}
